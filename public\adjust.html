<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Filter Editor</title>
    <style>
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .filter-editor {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 600px;
        }

        .controls {
            background: #f8f9fa;
            padding: 20px;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
        }

        .preview {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #fff;
        }

        .header {
            background: #343a40;
            color: white;
            padding: 15px 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 1.5rem;
        }

        .filter-group {
            margin-bottom: 20px;
        }

        .filter-group h3 {
            margin: 0 0 10px 0;
            font-size: 1rem;
            color: #495057;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        .filter-control {
            margin-bottom: 15px;
        }

        .filter-control label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .filter-control input[type="range"] {
            width: 100%;
            margin-bottom: 5px;
        }

        .filter-control .value-display {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: right;
        }

        .preset-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-bottom: 20px;
        }

        .preset-btn {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.2s;
        }

        .preset-btn:hover {
            background: #e9ecef;
        }

        .preset-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .preview-image {
            max-width: 100%;
            max-height: 400px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: filter 0.3s ease;
        }

        .image-selector {
            margin-bottom: 20px;
        }

        .image-selector select {
            width: 100%;
            padding: 8px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
        }

        .css-output {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }

        .css-output h4 {
            margin: 0 0 10px 0;
            font-size: 0.9rem;
            color: #495057;
        }

        .css-output code {
            display: block;
            background: #343a40;
            color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            word-break: break-all;
            white-space: pre-wrap;
        }

        .reset-btn {
            width: 100%;
            padding: 10px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            margin-top: 10px;
        }

        .reset-btn:hover {
            background: #c82333;
        }

        @media (max-width: 768px) {
            .filter-editor {
                grid-template-columns: 1fr;
            }

            .controls {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CSS <code>filter</code> Editor</h1>
        </div>
        <div class="filter-editor">
            <div class="controls">
                <div class="image-selector">
                    <label for="imageSelect">Preview Image:</label>
                    <select id="imageSelect">
                        <option value="https://assets.stoumann.dk/img/filter01.jpg">Image 1</option>
                        <option value="https://assets.stoumann.dk/img/filter02.jpg">Image 2</option>
                        <option value="https://assets.stoumann.dk/img/filter03.jpg">Image 3</option>
                        <option value="https://assets.stoumann.dk/img/filter04.jpg">Image 4</option>
                        <option value="https://assets.stoumann.dk/img/filter05.jpg">Image 5</option>
                    </select>
                </div>

                <div class="filter-group">
                    <h3>Presets</h3>
                    <div class="preset-buttons" id="presetButtons">
                        <!-- Preset buttons will be generated here -->
                    </div>
                </div>

                <div class="filter-group">
                    <h3>Basic Filters</h3>
                    <div class="filter-control">
                        <label for="blur">Blur</label>
                        <input type="range" id="blur" min="0" max="10" step="0.1" value="0">
                        <div class="value-display" id="blurValue">0px</div>
                    </div>
                    <div class="filter-control">
                        <label for="brightness">Brightness</label>
                        <input type="range" id="brightness" min="0" max="3" step="0.1" value="1">
                        <div class="value-display" id="brightnessValue">100%</div>
                    </div>
                    <div class="filter-control">
                        <label for="contrast">Contrast</label>
                        <input type="range" id="contrast" min="0" max="3" step="0.1" value="1">
                        <div class="value-display" id="contrastValue">100%</div>
                    </div>
                    <div class="filter-control">
                        <label for="grayscale">Grayscale</label>
                        <input type="range" id="grayscale" min="0" max="1" step="0.1" value="0">
                        <div class="value-display" id="grayscaleValue">0%</div>
                    </div>
                    <div class="filter-control">
                        <label for="hueRotate">Hue Rotate</label>
                        <input type="range" id="hueRotate" min="0" max="360" step="1" value="0">
                        <div class="value-display" id="hueRotateValue">0deg</div>
                    </div>
                    <div class="filter-control">
                        <label for="invert">Invert</label>
                        <input type="range" id="invert" min="0" max="1" step="0.1" value="0">
                        <div class="value-display" id="invertValue">0%</div>
                    </div>
                    <div class="filter-control">
                        <label for="opacity">Opacity</label>
                        <input type="range" id="opacity" min="0" max="1" step="0.1" value="1">
                        <div class="value-display" id="opacityValue">100%</div>
                    </div>
                    <div class="filter-control">
                        <label for="saturate">Saturate</label>
                        <input type="range" id="saturate" min="0" max="3" step="0.1" value="1">
                        <div class="value-display" id="saturateValue">100%</div>
                    </div>
                    <div class="filter-control">
                        <label for="sepia">Sepia</label>
                        <input type="range" id="sepia" min="0" max="1" step="0.1" value="0">
                        <div class="value-display" id="sepiaValue">0%</div>
                    </div>
                </div>

                <button class="reset-btn" id="resetBtn">Reset All Filters</button>

                <div class="css-output">
                    <h4>CSS Output:</h4>
                    <code id="cssOutput">filter: none;</code>
                </div>
            </div>

            <div class="preview">
                <img id="previewImage" class="preview-image" src="https://assets.stoumann.dk/img/filter01.jpg" alt="Preview Image">
            </div>
        </div>
    </div>

    <script>
        // Filter presets from the original CodePen
        const presets = [
            {
                "name": "watercolor",
                "description": "Watercolor effect",
                "value": "url('#squiggly-1') brightness(1.3) invert(0.17) saturate(2.6) sepia(0.25)",
                "values": {
                    "brightness": 1.3,
                    "invert": 0.17,
                    "saturate": 2.6,
                    "sepia": 0.25,
                    "url": "url('#squiggly-1')"
                }
            },
            {
                "name": "faded-photo",
                "description": "Faded photo effect",
                "value": "blur(0.2px) brightness(1.1) hue-rotate(5deg) opacity(0.9) saturate(1.3) sepia(0.4)",
                "values": {
                    "blur": 0.2,
                    "brightness": 1.1,
                    "hueRotate": 5,
                    "opacity": 0.9,
                    "saturate": 1.3,
                    "sepia": 0.4
                }
            },
            {
                "name": "old-horror",
                "description": "Old horror movie effect",
                "value": "url('#grain') grayscale(1) sepia(0.5) brightness(1.3) invert(0.8)",
                "values": {
                    "grayscale": 1,
                    "sepia": 0.5,
                    "brightness": 1.3,
                    "invert": 0.8,
                    "url": "url('#grain')"
                }
            },
            {
                "name": "old-grainy",
                "description": "Old grainy effect",
                "value": "url('#grain') grayscale(0.6) sepia(0.5) brightness(1.5)",
                "values": {
                    "grayscale": 0.6,
                    "sepia": 0.5,
                    "brightness": 1.5,
                    "url": "url('#grain')"
                }
            },
            {
                "name": "fade-out",
                "description": "Fade out effect",
                "value": "brightness(0.8) hue-rotate(350deg) saturate(3) blur(8px) contrast(0.6)",
                "values": {
                    "brightness": 0.8,
                    "hueRotate": 350,
                    "saturate": 3,
                    "blur": 8,
                    "contrast": 0.6
                }
            },
            {
                "name": "mist",
                "description": "Misty effect",
                "value": "url('#fluffy') brightness(0.8) saturate(0.8)",
                "values": {
                    "brightness": 0.8,
                    "saturate": 0.8,
                    "url": "url('#fluffy')"
                }
            }
        ];

        // Current filter values
        let currentFilters = {
            blur: 0,
            brightness: 1,
            contrast: 1,
            grayscale: 0,
            hueRotate: 0,
            invert: 0,
            opacity: 1,
            saturate: 1,
            sepia: 0
        };

        // DOM elements
        const previewImage = document.getElementById('previewImage');
        const imageSelect = document.getElementById('imageSelect');
        const cssOutput = document.getElementById('cssOutput');
        const presetButtons = document.getElementById('presetButtons');
        const resetBtn = document.getElementById('resetBtn');

        // Initialize the editor
        function init() {
            createPresetButtons();
            setupEventListeners();
            updateFilter();
        }

        // Create preset buttons
        function createPresetButtons() {
            presets.forEach(preset => {
                const button = document.createElement('button');
                button.className = 'preset-btn';
                button.textContent = preset.name.replace('-', ' ');
                button.title = preset.description;
                button.addEventListener('click', () => applyPreset(preset));
                presetButtons.appendChild(button);
            });
        }

        // Setup event listeners for all controls
        function setupEventListeners() {
            // Image selector
            imageSelect.addEventListener('change', (e) => {
                previewImage.src = e.target.value;
            });

            // Filter controls
            Object.keys(currentFilters).forEach(filterName => {
                const control = document.getElementById(filterName);
                if (control) {
                    control.addEventListener('input', (e) => {
                        currentFilters[filterName] = parseFloat(e.target.value);
                        updateValueDisplay(filterName, e.target.value);
                        updateFilter();
                        clearActivePreset();
                    });
                }
            });

            // Reset button
            resetBtn.addEventListener('click', resetFilters);
        }

        // Update value display for a filter
        function updateValueDisplay(filterName, value) {
            const display = document.getElementById(filterName + 'Value');
            if (display) {
                let displayValue = value;
                switch (filterName) {
                    case 'blur':
                        displayValue = value + 'px';
                        break;
                    case 'hueRotate':
                        displayValue = value + 'deg';
                        break;
                    case 'brightness':
                    case 'contrast':
                    case 'saturate':
                        displayValue = Math.round(value * 100) + '%';
                        break;
                    case 'grayscale':
                    case 'invert':
                    case 'opacity':
                    case 'sepia':
                        displayValue = Math.round(value * 100) + '%';
                        break;
                }
                display.textContent = displayValue;
            }
        }

        // Apply a preset
        function applyPreset(preset) {
            // Clear active preset buttons
            clearActivePreset();

            // Set active preset button
            event.target.classList.add('active');

            // Reset all filters first
            resetFilters(false);

            // Apply preset values
            Object.keys(preset.values).forEach(key => {
                if (key !== 'url' && currentFilters.hasOwnProperty(key)) {
                    currentFilters[key] = preset.values[key];
                    const control = document.getElementById(key);
                    if (control) {
                        control.value = preset.values[key];
                        updateValueDisplay(key, preset.values[key]);
                    }
                }
            });

            updateFilter();
        }

        // Clear active preset button
        function clearActivePreset() {
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.classList.remove('active');
            });
        }

        // Reset all filters
        function resetFilters(updateUI = true) {
            currentFilters = {
                blur: 0,
                brightness: 1,
                contrast: 1,
                grayscale: 0,
                hueRotate: 0,
                invert: 0,
                opacity: 1,
                saturate: 1,
                sepia: 0
            };

            if (updateUI) {
                Object.keys(currentFilters).forEach(filterName => {
                    const control = document.getElementById(filterName);
                    if (control) {
                        control.value = currentFilters[filterName];
                        updateValueDisplay(filterName, currentFilters[filterName]);
                    }
                });
                clearActivePreset();
            }

            updateFilter();
        }

        // Update the filter CSS and apply to image
        function updateFilter() {
            const filterParts = [];

            if (currentFilters.blur > 0) {
                filterParts.push(`blur(${currentFilters.blur}px)`);
            }
            if (currentFilters.brightness !== 1) {
                filterParts.push(`brightness(${currentFilters.brightness})`);
            }
            if (currentFilters.contrast !== 1) {
                filterParts.push(`contrast(${currentFilters.contrast})`);
            }
            if (currentFilters.grayscale > 0) {
                filterParts.push(`grayscale(${currentFilters.grayscale})`);
            }
            if (currentFilters.hueRotate > 0) {
                filterParts.push(`hue-rotate(${currentFilters.hueRotate}deg)`);
            }
            if (currentFilters.invert > 0) {
                filterParts.push(`invert(${currentFilters.invert})`);
            }
            if (currentFilters.opacity !== 1) {
                filterParts.push(`opacity(${currentFilters.opacity})`);
            }
            if (currentFilters.saturate !== 1) {
                filterParts.push(`saturate(${currentFilters.saturate})`);
            }
            if (currentFilters.sepia > 0) {
                filterParts.push(`sepia(${currentFilters.sepia})`);
            }

            const filterCSS = filterParts.length > 0 ? filterParts.join(' ') : 'none';

            // Apply filter to preview image
            previewImage.style.filter = filterCSS;

            // Update CSS output
            cssOutput.textContent = `filter: ${filterCSS};`;
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>

    <svg id="svgfilters" aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
		<defs>
			<filter id="teal-white" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.03 1"/>
					<feFuncG type="table" tableValues="0.57 1"/>
					<feFuncB type="table" tableValues="0.49 1"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="teal-lightgreen" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.03 0.8"/>
					<feFuncG type="table" tableValues="0.57 1"/>
					<feFuncB type="table" tableValues="0.49 0.53"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.26 0.95"/>
					<feFuncG type="table" tableValues="0.19 0.78"/>
					<feFuncB type="table" tableValues="0.11 0.59"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="purple-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.43 0.97"/>
					<feFuncG type="table" tableValues="0.06 0.88"/>
					<feFuncB type="table" tableValues="0.37 0.79"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="cherry-icecream" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.84 1"/>
					<feFuncG type="table" tableValues="0.05 0.94"/>
					<feFuncB type="table" tableValues="0.37 0.61"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="blackCurrant-and-mint" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.75 0.53"/>
					<feFuncG type="table" tableValues="0.25 0.97"/>
					<feFuncB type="table" tableValues="0.64 0.77"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.02 0.13 0.8"/>
					<feFuncG type="table" tableValues="0.02 0.47 0.97"/>
					<feFuncB type="table" tableValues="0.26 0.52 0.48"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="warm-sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.29 0.01 0.97"/>
					<feFuncG type="table" tableValues="0.12 0.52 0.94"/>
					<feFuncB type="table" tableValues="0.37 0.59 0.47"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="spring-grass" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0 0.38 0.92"/>
					<feFuncG type="table" tableValues="0.5 0.8 1"/>
					<feFuncB type="table" tableValues="0.5 0.56 0.74"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="red-sunset-with-purple" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.52 0.86 0.97"/>
					<feFuncG type="table" tableValues="0 0.08 0.81"/>
					<feFuncB type="table" tableValues="0.51 0.24 0.05"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="red-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.27 0.86 0.97"/>
					<feFuncG type="table" tableValues="0.01 0.08 0.81"/>
					<feFuncB type="table" tableValues="0.02 0.24 0.05"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="gold-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.27 0.86 1"/>
					<feFuncG type="table" tableValues="0.01 0.31 0.95"/>
					<feFuncB type="table" tableValues="0.02 0.02 0.02"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="dark-crimson-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.01 0.52 0.97"/>
					<feFuncG type="table" tableValues="0 0.05 0.81"/>
					<feFuncB type="table" tableValues="0.02 0.29 0.61"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="dark-blue-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.29 0.15 0.97"/>
					<feFuncG type="table" tableValues="0.04 0.39 0.93"/>
					<feFuncB type="table" tableValues="0.32 0.52 0.73"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="dark-green-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.25 0.39 0.96"/>
					<feFuncG type="table" tableValues="0.16 0.52 0.97"/>
					<feFuncB type="table" tableValues="0.06 0.39 0.78"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.98 0.3 0.25"/>
					<feFuncG type="table" tableValues="1 0.44 0.24"/>
					<feFuncB type="table" tableValues="0.91 0.62 0.39"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="warm-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.98 0.75 0.51"/>
					<feFuncG type="table" tableValues="1 0.45 0.11"/>
					<feFuncB type="table" tableValues="0.91 0.39 0.29"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="golden-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.98 1 0.94"/>
					<feFuncG type="table" tableValues="1 0.98 0.44"/>
					<feFuncB type="table" tableValues="0.91 0.43 0.02"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="purple-warm" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.52 0.97 1"/>
					<feFuncG type="table" tableValues="0 0.62 1"/>
					<feFuncB type="table" tableValues="0.51 0.39 0.89"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="green-pink-acid" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="1 0.98 0.1"/>
					<feFuncG type="table" tableValues="0.17 1 0.82"/>
					<feFuncB type="table" tableValues="0.7 0.84 0.67"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="yellow-blue-acid" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix"/>
				<feComponentTransfer in="colormatrix" result="componentTransfer">
					<feFuncR type="table" tableValues="0.01 0.97 0.89"/>
					<feFuncG type="table" tableValues="0.38 1 1"/>
					<feFuncB type="table" tableValues="1 0.89 0.01"/>
					<feFuncA type="table" tableValues="0 1"/>
				</feComponentTransfer>
				<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend"/>
			</filter>
			<filter id="noise" x="0%" y="0%" width="100%" height="100%">
				<feTurbulence baseFrequency="0.01 0.4" result="NOISE" numOctaves="2" />
				<feDisplacementMap in="SourceGraphic" in2="NOISE" scale="20" xChannelSelector="R" yChannelSelector="R"></feDisplacementMap>
			</filter>
			<filter id="squiggly-0">
				<feTurbulence id="turbulence1" baseFrequency="0.02" numOctaves="3" result="noise" seed="0" />
				<feDisplacementMap id="displacement" in="SourceGraphic" in2="noise" scale="6" />
			</filter>
			<filter id="squiggly-1">
				<feTurbulence id="turbulence2" baseFrequency="0.02" numOctaves="3" result="noise" seed="1" />
				<feDisplacementMap in="SourceGraphic" in2="noise" scale="8" />
			</filter>
			<filter id="squiggly-2">
				<feTurbulence id="turbulence3" baseFrequency="0.02" numOctaves="3" result="noise" seed="2" />
				<feDisplacementMap in="SourceGraphic" in2="noise" scale="6" />
			</filter>
			<filter id="squiggly-3">
				<feTurbulence id="turbulence4" baseFrequency="0.02" numOctaves="3" result="noise" seed="3" />
				<feDisplacementMap in="SourceGraphic" in2="noise" scale="8" />
			</filter>
			<filter id="squiggly-4">
				<feTurbulence id="turbulence5" baseFrequency="0.02" numOctaves="3" result="noise" seed="4" />
				<feDisplacementMap in="SourceGraphic" in2="noise" scale="6" />
			</filter>
			<filter id="posterize">
				<feComponentTransfer>
					<feFuncR type="discrete" tableValues="0 .5 1" />
				</feComponentTransfer>
			</filter>
			<filter id="dancing" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="linearRGB">
				<feMorphology operator="dilate" radius="4 4" in="SourceAlpha" result="morphology"/>
				<feFlood flood-color="#30597E" flood-opacity="1" result="flood"/>
				<feComposite in="flood" in2="morphology" operator="in" result="composite"/>
				<feComposite in="composite" in2="SourceAlpha" operator="out" result="composite1"/>
				<feTurbulence type="fractalNoise" baseFrequency="0.01 0.02" numOctaves="1" seed="0" stitchTiles="stitch" result="turbulence"/>
				<feDisplacementMap in="composite1" in2="turbulence" scale="17" xChannelSelector="A" yChannelSelector="A" result="displacementMap"/>
				<feMerge result="merge">
					<feMergeNode in="SourceGraphic"/>
					<feMergeNode in="displacementMap"/>
					</feMerge>
			</filter>
			<filter id="drops" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
				<feTurbulence type="turbulence" baseFrequency="0.05 0.05" numOctaves="1" seed="3" stitchTiles="stitch" result="turbulence"/>
				<feComposite in="turbulence" in2="SourceGraphic" operator="in" result="composite"/>
				<feColorMatrix type="matrix" values="1 0 0 0 0
					0 1 0 0 0
					0 0 1 0 0
					0 0 0 25 -2" in="composite" result="colormatrix"/>
				<feComposite in="SourceGraphic" in2="colormatrix" operator="in" result="composite1"/>
				<feGaussianBlur stdDeviation="3 3" in="composite1" result="blur"/>
				<feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20" lighting-color="#fffffd" in="blur" result="specularLighting">
					<feDistantLight azimuth="-90" elevation="150"/>
				</feSpecularLighting>
				<feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20" lighting-color="#cae1fe" in="blur" result="specularLighting1">
					<feDistantLight azimuth="90" elevation="150"/>
				</feSpecularLighting>
				<feSpecularLighting surfaceScale="7" specularConstant="1" specularExponent="35" lighting-color="#fcfeff" in="blur" result="specularLighting2">
					<fePointLight x="150" y="50" z="300"/>
				</feSpecularLighting>
				<feComposite in="specularLighting" in2="composite1" operator="in" result="composite2"/>
				<feComposite in="specularLighting2" in2="composite1" operator="in" result="composite3"/>
				<feComposite in="specularLighting1" in2="composite1" operator="in" result="composite4"/>
				<feBlend mode="multiply" in="composite4" in2="SourceGraphic" result="blend"/>
				<feBlend in="composite2" in2="blend" result="blend1"/>
				<feBlend in="composite3" in2="blend1" result="blend2"/>
			</filter>
			<filter id="grain">
				<feTurbulence baseFrequency="0.60,0.90" result="colorNoise" />
				<feColorMatrix in="colorNoise" type="matrix" values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0"/>
				<feComposite operator="in" in2="SourceGraphic" result="monoNoise"/>
				<feBlend in="SourceGraphic" in2="monoNoise" mode="multiply" />
			</filter>
			<filter id="fluffy" x="0%" y="0%" width="100%" height="100%">
				 <feTurbulence type="fractalNoise" baseFrequency="0.04" result="fluffyNoise" numOctaves="5" />
				<feColorMatrix in="fluffyNoise" type="matrix" values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0"/>
				<feComposite operator="in" in2="SourceGraphic" result="monoFluffyNoise"/>
				<feBlend in="SourceGraphic" in2="monoFluffyNoise" mode="screen" />
			</filter>
		</defs>
	</svg>
</body>
</html>